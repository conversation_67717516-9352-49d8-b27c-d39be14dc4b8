package repository

import (
	"context"
	"encoding/json"
	"fmt"
	"solve_api/internal/config"
	"solve_api/internal/model"
	"time"

	"github.com/redis/go-redis/v9"
)

type QuestionCacheRepository struct {
	rdb    *redis.Client
	mysqlRepo *QuestionRepository // MySQL作为持久化备份
}

// NewQuestionCacheRepository 创建题目缓存仓库实例
func NewQuestionCacheRepository(rdb *redis.Client, mysqlRepo *QuestionRepository) *QuestionCacheRepository {
	return &QuestionCacheRepository{
		rdb:       rdb,
		mysqlRepo: mysqlRepo,
	}
}

// Set 设置题目到Redis主存储，并异步持久化到MySQL
func (r *QuestionCacheRepository) Set(hash string, question *model.Question) error {
	ctx := context.Background()
	key := model.GenerateCacheKey(hash)

	// 序列化题目数据
	data, err := json.Marshal(question)
	if err != nil {
		return err
	}

	// 设置TTL
	ttl := time.Duration(config.GlobalConfig.App.CacheTTL) * time.Second
	if ttl <= 0 {
		ttl = 7 * 24 * time.Hour // 默认7天
	}

	// 1. 优先写入Redis主存储
	if r.rdb != nil {
		if err := r.rdb.Set(ctx, key, data, ttl).Err(); err != nil {
			// Redis写入失败，降级到MySQL
			fmt.Printf("⚠️ Redis写入失败，降级到MySQL: %v\n", err)
			return r.setToMySQLOnly(question)
		}

		// 2. 异步持久化到MySQL
		go r.asyncPersistToMySQL(question)
		return nil
	}

	// Redis不可用，直接写入MySQL
	return r.setToMySQLOnly(question)
}

// setToMySQLOnly 仅写入MySQL
func (r *QuestionCacheRepository) setToMySQLOnly(question *model.Question) error {
	if r.mysqlRepo == nil {
		return fmt.Errorf("both Redis and MySQL are unavailable")
	}

	// 检查是否已存在
	existing, err := r.mysqlRepo.GetByHash(question.Hash)
	if err != nil {
		return err
	}

	if existing != nil {
		// 已存在，更新
		return r.mysqlRepo.Update(question)
	}

	// 不存在，创建
	return r.mysqlRepo.Create(question)
}

// asyncPersistToMySQL 异步持久化到MySQL
func (r *QuestionCacheRepository) asyncPersistToMySQL(question *model.Question) {
	if r.mysqlRepo == nil {
		return
	}

	// 检查是否已存在
	existing, err := r.mysqlRepo.GetByHash(question.Hash)
	if err != nil {
		fmt.Printf("⚠️ MySQL持久化检查失败: %v\n", err)
		return
	}

	if existing != nil {
		// 已存在，更新
		if err := r.mysqlRepo.Update(question); err != nil {
			fmt.Printf("⚠️ MySQL持久化更新失败: %v\n", err)
		}
	} else {
		// 不存在，创建
		if err := r.mysqlRepo.Create(question); err != nil {
			fmt.Printf("⚠️ MySQL持久化创建失败: %v\n", err)
		}
	}
}

// Get 从Redis主存储获取题目，如果不存在则从MySQL获取并回写Redis
func (r *QuestionCacheRepository) Get(hash string) (*model.Question, error) {
	ctx := context.Background()
	key := model.GenerateCacheKey(hash)

	// 1. 优先从Redis主存储获取
	if r.rdb != nil {
		data, err := r.rdb.Get(ctx, key).Result()
		if err == nil {
			// Redis命中，反序列化数据
			var question model.Question
			if err := json.Unmarshal([]byte(data), &question); err != nil {
				fmt.Printf("⚠️ Redis数据反序列化失败: %v\n", err)
				// 反序列化失败，尝试从MySQL获取
				return r.getFromMySQLAndCache(hash)
			}
			return &question, nil
		}

		if err != redis.Nil {
			fmt.Printf("⚠️ Redis读取失败: %v\n", err)
		}
		// Redis未命中或读取失败，尝试从MySQL获取
	}

	// 2. 从MySQL获取并回写Redis
	return r.getFromMySQLAndCache(hash)
}

// getFromMySQLAndCache 从MySQL获取数据并回写到Redis
func (r *QuestionCacheRepository) getFromMySQLAndCache(hash string) (*model.Question, error) {
	if r.mysqlRepo == nil {
		return nil, nil // MySQL不可用，返回未找到
	}

	// 从MySQL获取
	question, err := r.mysqlRepo.GetByHash(hash)
	if err != nil {
		return nil, err
	}

	if question == nil {
		return nil, nil // MySQL中也不存在
	}

	// 异步回写到Redis
	go r.asyncCacheToRedis(hash, question)

	return question, nil
}

// asyncCacheToRedis 异步回写到Redis
func (r *QuestionCacheRepository) asyncCacheToRedis(hash string, question *model.Question) {
	if r.rdb == nil {
		return
	}

	ctx := context.Background()
	key := model.GenerateCacheKey(hash)

	// 序列化数据
	data, err := json.Marshal(question)
	if err != nil {
		fmt.Printf("⚠️ 回写Redis序列化失败: %v\n", err)
		return
	}

	// 设置TTL
	ttl := time.Duration(config.GlobalConfig.App.CacheTTL) * time.Second
	if ttl <= 0 {
		ttl = 7 * 24 * time.Hour // 默认7天
	}

	// 回写到Redis
	if err := r.rdb.Set(ctx, key, data, ttl).Err(); err != nil {
		fmt.Printf("⚠️ 回写Redis失败: %v\n", err)
	}
}

// Delete 删除题目（同时删除Redis和MySQL）
func (r *QuestionCacheRepository) Delete(hash string) error {
	var redisErr, mysqlErr error

	// 1. 删除Redis中的数据
	if r.rdb != nil {
		ctx := context.Background()
		key := model.GenerateCacheKey(hash)
		redisErr = r.rdb.Del(ctx, key).Err()
		if redisErr != nil {
			fmt.Printf("⚠️ Redis删除失败: %v\n", redisErr)
		}
	}

	// 2. 删除MySQL中的数据
	if r.mysqlRepo != nil {
		question, err := r.mysqlRepo.GetByHash(hash)
		if err == nil && question != nil {
			mysqlErr = r.mysqlRepo.Delete(question.ID)
			if mysqlErr != nil {
				fmt.Printf("⚠️ MySQL删除失败: %v\n", mysqlErr)
			}
		}
	}

	// 如果两者都失败，返回错误
	if redisErr != nil && mysqlErr != nil {
		return fmt.Errorf("both Redis and MySQL delete failed: redis=%v, mysql=%v", redisErr, mysqlErr)
	}

	return nil
}

// Exists 检查缓存是否存在
func (r *QuestionCacheRepository) Exists(hash string) (bool, error) {
	if r.rdb == nil {
		return false, nil // Redis不可用时返回false
	}

	ctx := context.Background()
	key := model.GenerateCacheKey(hash)

	count, err := r.rdb.Exists(ctx, key).Result()
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// SetTTL 设置缓存过期时间
func (r *QuestionCacheRepository) SetTTL(hash string, ttl time.Duration) error {
	if r.rdb == nil {
		return nil // Redis不可用时不报错
	}

	ctx := context.Background()
	key := model.GenerateCacheKey(hash)

	return r.rdb.Expire(ctx, key, ttl).Err()
}

// GetTTL 获取缓存剩余时间
func (r *QuestionCacheRepository) GetTTL(hash string) (time.Duration, error) {
	if r.rdb == nil {
		return 0, nil // Redis不可用时返回0
	}

	ctx := context.Background()
	key := model.GenerateCacheKey(hash)

	return r.rdb.TTL(ctx, key).Result()
}

// GetKeys 获取所有题目缓存键
func (r *QuestionCacheRepository) GetKeys(pattern string) ([]string, error) {
	if r.rdb == nil {
		return nil, nil // Redis不可用时返回空
	}

	ctx := context.Background()
	if pattern == "" {
		pattern = "question:*"
	}

	return r.rdb.Keys(ctx, pattern).Result()
}

// Clear 清空所有题目缓存
func (r *QuestionCacheRepository) Clear() error {
	if r.rdb == nil {
		return nil // Redis不可用时不报错
	}

	ctx := context.Background()
	keys, err := r.GetKeys("question:*")
	if err != nil {
		return err
	}

	if len(keys) == 0 {
		return nil
	}

	return r.rdb.Del(ctx, keys...).Err()
}

// GetCacheStats 获取缓存统计信息
func (r *QuestionCacheRepository) GetCacheStats() (map[string]interface{}, error) {
	if r.rdb == nil {
		return map[string]interface{}{
			"redis_available": false,
			"total_keys":      0,
		}, nil
	}

	ctx := context.Background()
	
	// 获取所有题目缓存键
	keys, err := r.GetKeys("question:*")
	if err != nil {
		return nil, err
	}

	stats := map[string]interface{}{
		"redis_available": true,
		"total_keys":      len(keys),
	}

	// 获取Redis内存使用情况
	info, err := r.rdb.Info(ctx, "memory").Result()
	if err == nil {
		stats["memory_info"] = info
	}

	return stats, nil
}

// BatchSet 批量设置到Redis主存储，并异步持久化到MySQL
func (r *QuestionCacheRepository) BatchSet(questions map[string]*model.Question) error {
	// 1. 批量写入Redis
	var redisErr error
	if r.rdb != nil {
		ctx := context.Background()
		pipe := r.rdb.Pipeline()

		ttl := time.Duration(config.GlobalConfig.App.CacheTTL) * time.Second
		if ttl <= 0 {
			ttl = 7 * 24 * time.Hour // 默认7天
		}

		for hash, question := range questions {
			key := model.GenerateCacheKey(hash)
			data, err := json.Marshal(question)
			if err != nil {
				continue // 跳过序列化失败的数据
			}
			pipe.Set(ctx, key, data, ttl)
		}

		_, redisErr = pipe.Exec(ctx)
		if redisErr != nil {
			fmt.Printf("⚠️ Redis批量写入失败: %v\n", redisErr)
		}
	}

	// 2. 异步批量持久化到MySQL
	go r.asyncBatchPersistToMySQL(questions)

	// 如果Redis写入失败且MySQL不可用，返回错误
	if redisErr != nil && r.mysqlRepo == nil {
		return redisErr
	}

	return nil
}

// asyncBatchPersistToMySQL 异步批量持久化到MySQL
func (r *QuestionCacheRepository) asyncBatchPersistToMySQL(questions map[string]*model.Question) {
	if r.mysqlRepo == nil {
		return
	}

	for _, question := range questions {
		// 检查是否已存在
		existing, err := r.mysqlRepo.GetByHash(question.Hash)
		if err != nil {
			fmt.Printf("⚠️ MySQL批量持久化检查失败: %v\n", err)
			continue
		}

		if existing != nil {
			// 已存在，更新
			if err := r.mysqlRepo.Update(question); err != nil {
				fmt.Printf("⚠️ MySQL批量持久化更新失败: %v\n", err)
			}
		} else {
			// 不存在，创建
			if err := r.mysqlRepo.Create(question); err != nil {
				fmt.Printf("⚠️ MySQL批量持久化创建失败: %v\n", err)
			}
		}
	}
}

// BatchGet 批量获取，优先从Redis，缺失的从MySQL补充
func (r *QuestionCacheRepository) BatchGet(hashes []string) (map[string]*model.Question, error) {
	questions := make(map[string]*model.Question)
	var missedHashes []string

	// 1. 先从Redis批量获取
	if r.rdb != nil {
		ctx := context.Background()
		pipe := r.rdb.Pipeline()

		// 构建键列表
		keys := make([]string, len(hashes))
		for i, hash := range hashes {
			keys[i] = model.GenerateCacheKey(hash)
		}

		// 批量获取
		for _, key := range keys {
			pipe.Get(ctx, key)
		}

		results, err := pipe.Exec(ctx)
		if err != nil && err != redis.Nil {
			fmt.Printf("⚠️ Redis批量读取失败: %v\n", err)
		} else {
			// 解析Redis结果
			for i, result := range results {
				if cmd, ok := result.(*redis.StringCmd); ok {
					data, err := cmd.Result()
					if err == nil {
						var question model.Question
						if json.Unmarshal([]byte(data), &question) == nil {
							questions[hashes[i]] = &question
						} else {
							missedHashes = append(missedHashes, hashes[i])
						}
					} else {
						missedHashes = append(missedHashes, hashes[i])
					}
				} else {
					missedHashes = append(missedHashes, hashes[i])
				}
			}
		}
	} else {
		// Redis不可用，所有hash都需要从MySQL获取
		missedHashes = hashes
	}

	// 2. 从MySQL获取缺失的数据
	if len(missedHashes) > 0 && r.mysqlRepo != nil {
		mysqlQuestions := r.batchGetFromMySQL(missedHashes)

		// 合并结果并异步回写Redis
		for hash, question := range mysqlQuestions {
			questions[hash] = question
			go r.asyncCacheToRedis(hash, question)
		}
	}

	return questions, nil
}

// batchGetFromMySQL 从MySQL批量获取数据
func (r *QuestionCacheRepository) batchGetFromMySQL(hashes []string) map[string]*model.Question {
	questions := make(map[string]*model.Question)

	for _, hash := range hashes {
		question, err := r.mysqlRepo.GetByHash(hash)
		if err == nil && question != nil {
			questions[hash] = question
		}
	}

	return questions
}
