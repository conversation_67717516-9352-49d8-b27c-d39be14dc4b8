package repository

import (
	"errors"
	"solve_api/internal/model"

	"gorm.io/gorm"
)

type QuestionRepository struct {
	db *gorm.DB
}

// NewQuestionRepository 创建题目仓库实例
func NewQuestionRepository(db *gorm.DB) *QuestionRepository {
	return &QuestionRepository{db: db}
}

// Create 创建题目
func (r *QuestionRepository) Create(question *model.Question) error {
	// 只有在哈希值为空时才生成哈希值
	if question.Hash == "" {
		question.GenerateHash()
	}
	return r.db.Create(question).Error
}

// GetByID 根据ID获取题目
func (r *QuestionRepository) GetByID(id uint) (*model.Question, error) {
	var question model.Question
	err := r.db.First(&question, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &question, nil
}

// GetByHash 根据哈希值获取题目
func (r *QuestionRepository) GetByHash(hash string) (*model.Question, error) {
	var question model.Question
	err := r.db.Where("hash = ?", hash).First(&question).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &question, nil
}

// Update 更新题目
func (r *QuestionRepository) Update(question *model.Question) error {
	return r.db.Save(question).Error
}

// Delete 软删除题目
func (r *QuestionRepository) Delete(id uint) error {
	return r.db.Delete(&model.Question{}, id).Error
}

// List 获取题目列表
func (r *QuestionRepository) List(offset, limit int) ([]*model.Question, int64, error) {
	var questions []*model.Question
	var total int64

	// 获取总数
	if err := r.db.Model(&model.Question{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := r.db.Offset(offset).Limit(limit).Order("created_at DESC").Find(&questions).Error
	if err != nil {
		return nil, 0, err
	}

	return questions, total, nil
}

// GetBySubject 根据学科获取题目列表
func (r *QuestionRepository) GetBySubject(subject string, offset, limit int) ([]*model.Question, int64, error) {
	var questions []*model.Question
	var total int64

	// 获取总数
	if err := r.db.Model(&model.Question{}).Where("subject = ?", subject).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := r.db.Where("subject = ?", subject).
		Offset(offset).
		Limit(limit).
		Order("created_at DESC").
		Find(&questions).Error
	if err != nil {
		return nil, 0, err
	}

	return questions, total, nil
}

// GetByGrade 根据年级获取题目列表
func (r *QuestionRepository) GetByGrade(grade string, offset, limit int) ([]*model.Question, int64, error) {
	var questions []*model.Question
	var total int64

	// 获取总数
	if err := r.db.Model(&model.Question{}).Where("grade = ?", grade).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := r.db.Where("grade = ?", grade).
		Offset(offset).
		Limit(limit).
		Order("created_at DESC").
		Find(&questions).Error
	if err != nil {
		return nil, 0, err
	}

	return questions, total, nil
}

// GetByDifficulty 根据难度获取题目列表
func (r *QuestionRepository) GetByDifficulty(difficulty int, offset, limit int) ([]*model.Question, int64, error) {
	var questions []*model.Question
	var total int64

	// 获取总数
	if err := r.db.Model(&model.Question{}).Where("difficulty = ?", difficulty).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := r.db.Where("difficulty = ?", difficulty).
		Offset(offset).
		Limit(limit).
		Order("created_at DESC").
		Find(&questions).Error
	if err != nil {
		return nil, 0, err
	}

	return questions, total, nil
}

// Search 搜索题目
func (r *QuestionRepository) Search(keyword string, offset, limit int) ([]*model.Question, int64, error) {
	var questions []*model.Question
	var total int64

	searchPattern := "%" + keyword + "%"

	// 获取总数
	if err := r.db.Model(&model.Question{}).
		Where("content LIKE ? OR analysis LIKE ? OR answer LIKE ?", searchPattern, searchPattern, searchPattern).
		Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := r.db.Where("content LIKE ? OR analysis LIKE ? OR answer LIKE ?", searchPattern, searchPattern, searchPattern).
		Offset(offset).
		Limit(limit).
		Order("created_at DESC").
		Find(&questions).Error
	if err != nil {
		return nil, 0, err
	}

	return questions, total, nil
}

// GetTotalCount 获取题目总数
func (r *QuestionRepository) GetTotalCount() (int64, error) {
	var count int64
	err := r.db.Model(&model.Question{}).Count(&count).Error
	return count, err
}

// GetCountBySubject 获取各学科题目数量
func (r *QuestionRepository) GetCountBySubject() (map[string]int64, error) {
	type SubjectCount struct {
		Subject string `json:"subject"`
		Count   int64  `json:"count"`
	}

	var results []SubjectCount
	err := r.db.Model(&model.Question{}).
		Select("subject, COUNT(*) as count").
		Group("subject").
		Find(&results).Error
	if err != nil {
		return nil, err
	}

	countMap := make(map[string]int64)
	for _, result := range results {
		countMap[result.Subject] = result.Count
	}

	return countMap, nil
}

// GetCountByGrade 获取各年级题目数量
func (r *QuestionRepository) GetCountByGrade() (map[string]int64, error) {
	type GradeCount struct {
		Grade string `json:"grade"`
		Count int64  `json:"count"`
	}

	var results []GradeCount
	err := r.db.Model(&model.Question{}).
		Select("grade, COUNT(*) as count").
		Group("grade").
		Find(&results).Error
	if err != nil {
		return nil, err
	}

	countMap := make(map[string]int64)
	for _, result := range results {
		countMap[result.Grade] = result.Count
	}

	return countMap, nil
}

// GetCountByDifficulty 获取各难度题目数量
func (r *QuestionRepository) GetCountByDifficulty() (map[int]int64, error) {
	type DifficultyCount struct {
		Difficulty int   `json:"difficulty"`
		Count      int64 `json:"count"`
	}

	var results []DifficultyCount
	err := r.db.Model(&model.Question{}).
		Select("difficulty, COUNT(*) as count").
		Group("difficulty").
		Find(&results).Error
	if err != nil {
		return nil, err
	}

	countMap := make(map[int]int64)
	for _, result := range results {
		countMap[result.Difficulty] = result.Count
	}

	return countMap, nil
}

// ExistsByHash 检查哈希值是否已存在
func (r *QuestionRepository) ExistsByHash(hash string) (bool, error) {
	var count int64
	err := r.db.Model(&model.Question{}).Where("hash = ?", hash).Count(&count).Error
	return count > 0, err
}
