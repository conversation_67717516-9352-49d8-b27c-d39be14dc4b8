package service

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"solve_api/internal/model"
	"solve_api/internal/repository"
	"time"
)

type AIService struct {
	modelConfigRepo *repository.ModelConfigRepository
	aiLogService    *AILogService
}

// NewAIService 创建AI服务实例
func NewAIService(modelConfigRepo *repository.ModelConfigRepository, aiLogService *AILogService) *AIService {
	return &AIService{
		modelConfigRepo: modelConfigRepo,
		aiLogService:    aiLogService,
	}
}

// QwenVLResult Qwen-VL调用结果
type QwenVLResult struct {
	Structure   *model.QuestionStructure
	RawContent  string // 原始content字符串
	LogID       string // 日志ID
}

// DeepseekResult Deepseek调用结果
type DeepseekResult struct {
	Analysis   string
	Answer     string
	RawContent string // 原始content字符串
}

// DeepSeek请求结构体定义（使用标准OpenAI格式）
type DeepSeekMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type DeepSeekResponseFormat struct {
	Type string `json:"type"`
}

type DeepSeekRequest struct {
	Model          string                   `json:"model"`
	Messages       []DeepSeekMessage        `json:"messages"`
	Temperature    *float64                 `json:"temperature,omitempty"`
	TopP           *float64                 `json:"top_p,omitempty"`
	MaxTokens      *int                     `json:"max_tokens,omitempty"`
	ResponseFormat *DeepSeekResponseFormat  `json:"response_format,omitempty"`
	FrequencyPenalty *float64               `json:"frequency_penalty,omitempty"`
	PresencePenalty  *float64               `json:"presence_penalty,omitempty"`
	Stream         *bool                    `json:"stream,omitempty"`
	Stop           interface{}              `json:"stop,omitempty"`
}

// CallQwenVL 调用Qwen-VL模型进行图像识别
func (s *AIService) CallQwenVL(imageURL string) (*QwenVLResult, error) {
	// 创建日志条目
	var logID string
	if s.aiLogService != nil {
		logID = s.aiLogService.CreateLogEntry()
	}
	// 获取模型配置
	modelConfig, err := s.modelConfigRepo.GetEnabledByName(model.ModelNameQwenVLPlus)
	if err != nil {
		return nil, fmt.Errorf("获取Qwen-VL模型配置失败: %w", err)
	}
	if modelConfig == nil {
		return nil, fmt.Errorf("Qwen-VL模型未配置或未启用")
	}

	// 构建请求参数
	params, err := modelConfig.GetParamsMap()
	if err != nil {
		return nil, fmt.Errorf("解析模型参数失败: %w", err)
	}

	// 从参数中获取动态配置的消息内容
	systemMessage := "精准且完整的识别题目内容，严格标准返回json格式。示例{\"question_type\": \"单选题or多选题or判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\"}}"
	userMessage := "question_text内的值不应该出现题目类型以及问题序号。"

	// 如果参数中有自定义消息，则使用自定义消息
	if sysMsg, exists := params["system_message"]; exists {
		if sysStr, ok := sysMsg.(string); ok && sysStr != "" {
			systemMessage = sysStr
		}
	}
	if usrMsg, exists := params["user_message"]; exists {
		if usrStr, ok := usrMsg.(string); ok && usrStr != "" {
			userMessage = usrStr
		}
	}

	// 获取模型名称
	modelName := "qwen-vl-plus" // 默认值
	if model, exists := params["model"]; exists {
		if modelStr, ok := model.(string); ok && modelStr != "" {
			modelName = modelStr
		}
	}

	// 添加Qwen API支持的技术约束参数（放在parameters对象中）
	// 严格按照Qwen API文档，只添加支持的技术参数
	parameters := make(map[string]interface{})
	supportedParams := []string{
		"temperature", "top_p", "top_k", "do_sample",
		"response_format", "frequency_penalty", "presence_penalty",
		"result_format",
	}

	for _, paramName := range supportedParams {
		if value, exists := params[paramName]; exists {
			parameters[paramName] = value
		}
	}

	// 确保result_format为message（DashScope格式要求）
	parameters["result_format"] = "message"

	// 定义消息结构体以确保字段顺序
	type MessageContent struct {
		Image string `json:"image,omitempty"`
		Text  string `json:"text,omitempty"`
	}

	type Message struct {
		Role    string           `json:"role"`
		Content interface{}      `json:"content"`
	}

	type InputData struct {
		Messages []Message `json:"messages"`
	}

	type RequestBody struct {
		Model      string      `json:"model"`
		Parameters interface{} `json:"parameters"`
		Input      InputData   `json:"input"`
	}

	// 构建符合Qwen API DashScope格式的请求体
	// 按照要求的顺序：1.model 2.parameters 3.input.messages
	requestBody := RequestBody{
		Model:      modelName,
		Parameters: parameters,
		Input: InputData{
			Messages: []Message{
				{
					Role:    "system",
					Content: systemMessage, // 取数据库内配置的system_message
				},
				{
					Role: "user",
					Content: []MessageContent{
						{
							Image: imageURL, // 取用户在业务中提交的图片url
						},
						{
							Text: userMessage, // 取数据库内配置的user_message
						},
					},
				},
			},
		},
	}

	// 记录请求数据到日志
	if s.aiLogService != nil {
		// 直接使用结构体保持字段顺序
		s.aiLogService.LogQwenRequest(logID, requestBody)
	}

	// 打印发送给Qwen的原始请求数据用于调试
	requestBytes, _ := json.MarshalIndent(requestBody, "", "  ")
	fmt.Printf("🚀 发送给Qwen的原始请求数据:\n%s\n", string(requestBytes))

	// 临时硬编码正确的DashScope多模态API URL进行测试
	correctAPIURL := "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation"

	// 发送请求
	response, err := s.sendRequest(correctAPIURL, modelConfig.ApiKey, requestBody)
	if err != nil {
		return nil, fmt.Errorf("调用Qwen-VL模型失败: %w", err)
	}

	// 解析响应
	var qwenResponse model.QwenVLResponse
	if err := json.Unmarshal(response, &qwenResponse); err != nil {
		return nil, fmt.Errorf("解析Qwen-VL响应失败: %w", err)
	}

	if len(qwenResponse.Output.Choices) == 0 {
		return nil, fmt.Errorf("Qwen-VL模型返回空结果")
	}

	// 打印Qwen原始content数据用于调试
	rawContent := qwenResponse.Output.Choices[0].Message.Content
	contentBytes, _ := json.Marshal(rawContent)
	fmt.Printf("🔍 Qwen原始Content数据: %s\n", string(contentBytes))

	// 使用新的方法提取内容字符串
	content := qwenResponse.GetContentString()

	// 记录响应数据到日志
	if s.aiLogService != nil {
		s.aiLogService.LogQwenResponse(logID, content)
	}
	if content == "" {
		return nil, fmt.Errorf("Qwen-VL模型返回空内容")
	}

	// 尝试解析JSON格式的响应
	var structure model.QuestionStructure
	if err := json.Unmarshal([]byte(content), &structure); err != nil {
		// 如果不是JSON格式，则作为纯文本处理
		structure = model.QuestionStructure{
			QuestionText: content,
			RawContent:   content,
			Subject:      "未知",
			Grade:        "未知",
			Difficulty:   3, // 默认中等难度
			Content:      content, // 向后兼容
		}
	} else {
		// 确保向后兼容
		if structure.Content == "" && structure.QuestionText != "" {
			structure.Content = structure.QuestionText
		}
		// 如果没有原始内容，使用题目文本
		if structure.RawContent == "" {
			structure.RawContent = structure.QuestionText
		}
	}

	return &QwenVLResult{
		Structure:  &structure,
		RawContent: content,
		LogID:      logID,
	}, nil
}

// CallDeepseek 调用Deepseek模型进行题目解析
func (s *AIService) CallDeepseek(qwenResult *QwenVLResult) (*DeepseekResult, error) {
	return s.CallDeepseekWithLogID(qwenResult, "")
}

// CallDeepseekWithLogID 调用Deepseek模型进行题目解析（带日志ID）
func (s *AIService) CallDeepseekWithLogID(qwenResult *QwenVLResult, logID string) (*DeepseekResult, error) {
	// 获取模型配置
	modelConfig, err := s.modelConfigRepo.GetEnabledByName(model.ModelNameDeepseekChat)
	if err != nil {
		return nil, fmt.Errorf("获取Deepseek模型配置失败: %w", err)
	}
	if modelConfig == nil {
		return nil, fmt.Errorf("Deepseek模型未配置或未启用")
	}

	// 构建请求参数
	params, err := modelConfig.GetParamsMap()
	if err != nil {
		return nil, fmt.Errorf("解析模型参数失败: %w", err)
	}

	// 获取配置的消息内容
	systemMessage, _ := params["system_message"].(string)
	if systemMessage == "" {
		systemMessage = "你是一个专业的题目解析助手，请对提供的题目进行详细解析。"
	}

	userMessage, _ := params["user_message"].(string)
	if userMessage == "" {
		userMessage = "请以JSON格式返回，包含analysis（详细解题思路）和answer（最终答案）字段。"
	}

	// 构建完整的用户消息，包含Qwen识别的完整结构化数据
	qwenDataJSON, _ := json.MarshalIndent(qwenResult.Structure, "", "  ")
	fullUserMessage := fmt.Sprintf(`%s

以下是图像识别系统提取的题目信息：
%s

原始识别内容：
%s

请基于以上信息进行详细的题目解析。`, userMessage, string(qwenDataJSON), qwenResult.RawContent)

	// 构建标准OpenAI格式的请求体
	requestBody := DeepSeekRequest{
		Model: modelConfig.Name,
		Messages: []DeepSeekMessage{
			{
				Role:    "system",
				Content: systemMessage,
			},
			{
				Role:    "user",
				Content: fullUserMessage,
			},
		},
	}

	// 从参数中提取标准字段
	if temp, ok := params["temperature"].(float64); ok {
		requestBody.Temperature = &temp
	}
	if topP, ok := params["top_p"].(float64); ok {
		requestBody.TopP = &topP
	}
	if maxTokens, ok := params["max_tokens"].(float64); ok {
		maxTokensInt := int(maxTokens)
		requestBody.MaxTokens = &maxTokensInt
	}
	if maxTokens, ok := params["max_tokens"].(int); ok {
		requestBody.MaxTokens = &maxTokens
	}

	// 处理response_format参数
	if responseFormat, ok := params["response_format"].(map[string]interface{}); ok {
		if formatType, exists := responseFormat["type"].(string); exists {
			requestBody.ResponseFormat = &DeepSeekResponseFormat{Type: formatType}
		}
	} else if responseFormat, ok := params["response_format"].(map[string]string); ok {
		if formatType, exists := responseFormat["type"]; exists {
			requestBody.ResponseFormat = &DeepSeekResponseFormat{Type: formatType}
		}
	}

	// 处理惩罚参数
	if freqPenalty, ok := params["frequency_penalty"].(float64); ok {
		requestBody.FrequencyPenalty = &freqPenalty
	}
	if presPenalty, ok := params["presence_penalty"].(float64); ok {
		requestBody.PresencePenalty = &presPenalty
	}

	// 记录请求数据到日志
	if s.aiLogService != nil && logID != "" {
		s.aiLogService.LogDeepSeekRequest(logID, requestBody)
	}

	// 打印发送给DeepSeek的原始请求数据用于调试
	requestBytes, _ := json.MarshalIndent(requestBody, "", "  ")
	fmt.Printf("🚀 发送给DeepSeek的原始请求数据:\n%s\n", string(requestBytes))

	// 发送请求
	response, err := s.sendRequest(modelConfig.ApiURL, modelConfig.ApiKey, requestBody)
	if err != nil {
		return nil, fmt.Errorf("调用Deepseek模型失败: %w", err)
	}

	// 解析响应
	var deepseekResponse model.DeepseekResponse
	if err := json.Unmarshal(response, &deepseekResponse); err != nil {
		return nil, fmt.Errorf("解析Deepseek响应失败: %w", err)
	}

	if len(deepseekResponse.Choices) == 0 {
		return nil, fmt.Errorf("Deepseek模型返回空结果")
	}

	content := deepseekResponse.Choices[0].Message.Content

	// 打印DeepSeek原始Content数据用于调试
	fmt.Printf("🔍 DeepSeek原始Content数据: %s\n", content)

	// 记录响应数据到日志
	if s.aiLogService != nil && logID != "" {
		s.aiLogService.LogDeepSeekResponse(logID, content)
	}

	// 尝试解析JSON格式的响应
	var result struct {
		Analysis string `json:"analysis"`
		Answer   string `json:"answer"`
	}

	if err := json.Unmarshal([]byte(content), &result); err != nil {
		// 如果不是JSON格式，则作为纯文本处理
		return &DeepseekResult{
			Analysis:   content,
			Answer:     "请参考解析内容",
			RawContent: content,
		}, nil
	}

	return &DeepseekResult{
		Analysis:   result.Analysis,
		Answer:     result.Answer,
		RawContent: content,
	}, nil
}

// sendRequest 发送HTTP请求
func (s *AIService) sendRequest(apiURL, apiKey string, requestBody interface{}) ([]byte, error) {
	// 序列化请求体
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("序列化请求体失败: %w", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", apiURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+apiKey)

	// 发送请求
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("❌ HTTP请求发送失败: %v\n", err)
		return nil, fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ 读取响应失败: %v\n", err)
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 打印详细的响应信息用于调试
	fmt.Printf("📋 API响应状态码: %d\n", resp.StatusCode)
	fmt.Printf("📋 API响应头: %+v\n", resp.Header)
	fmt.Printf("📋 API响应内容: %s\n", string(body))

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		fmt.Printf("❌ API请求失败，状态码: %d, 响应: %s\n", resp.StatusCode, string(body))
		return nil, fmt.Errorf("API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	return body, nil
}

// ValidateImageURL 验证图片URL是否可访问
func (s *AIService) ValidateImageURL(imageURL string) error {
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	resp, err := client.Head(imageURL)
	if err != nil {
		return fmt.Errorf("无法访问图片URL: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("图片URL返回错误状态码: %d", resp.StatusCode)
	}

	// 检查Content-Type是否为图片
	contentType := resp.Header.Get("Content-Type")
	if contentType == "" {
		return fmt.Errorf("无法确定文件类型")
	}

	validTypes := []string{
		"image/jpeg",
		"image/jpg", 
		"image/png",
		"image/gif",
		"image/bmp",
		"image/webp",
	}

	for _, validType := range validTypes {
		if contentType == validType {
			return nil
		}
	}

	return fmt.Errorf("不支持的图片格式: %s", contentType)
}
