package main

import (
	"fmt"
	"log"
	"solve_api/internal/config"
	"solve_api/internal/database"
	"solve_api/internal/model"
)

func main() {
	fmt.Println("🔧 初始化价格配置...")

	// 1. 加载配置
	cfg, err := config.LoadConfig("config/config.yaml")
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 2. 初始化数据库
	if err := database.InitMySQL(&cfg.Database.MySQL); err != nil {
		log.Fatalf("初始化数据库失败: %v", err)
	}
	defer database.CloseMySQL()

	db := database.GetDB()

	// 3. 检查是否已有价格配置
	var count int64
	err = db.Model(&model.PriceConfig{}).Where("service_type = ?", 1).Count(&count).Error
	if err != nil {
		log.Fatalf("检查价格配置失败: %v", err)
	}

	if count > 0 {
		fmt.Printf("✅ 已存在 %d 条价格配置\n", count)
		return
	}

	// 4. 创建默认价格配置
	fmt.Println("📝 创建默认价格配置...")

	// 默认价格配置（用户ID为0表示全局默认配置）
	defaultPriceConfig := &model.PriceConfig{
		UserID:      0,  // 0表示全局默认
		ServiceType: 1,  // 1表示拍照搜题服务
		Price:       0.01, // 每次调用0.01元
	}

	if err := db.Create(defaultPriceConfig).Error; err != nil {
		log.Fatalf("创建默认价格配置失败: %v", err)
	}

	fmt.Printf("✅ 默认价格配置创建成功 (ID: %d)\n", defaultPriceConfig.ID)

	// 5. 为现有用户创建个性化价格配置（可选）
	fmt.Println("📝 为现有用户创建价格配置...")

	var users []model.User
	if err := db.Find(&users).Error; err != nil {
		log.Printf("⚠️ 查询用户失败: %v", err)
	} else {
		for _, user := range users {
			userPriceConfig := &model.PriceConfig{
				UserID:      user.ID,
				ServiceType: 1,
				Price:       0.01, // 与默认价格相同
			}

			if err := db.Create(userPriceConfig).Error; err != nil {
				log.Printf("⚠️ 为用户 %d 创建价格配置失败: %v", user.ID, err)
			} else {
				fmt.Printf("✅ 用户 %d (%s) 价格配置创建成功\n", user.ID, user.Phone)
			}
		}
	}

	// 6. 验证配置
	fmt.Println("\n🔍 验证价格配置...")
	var configs []model.PriceConfig
	if err := db.Where("service_type = ?", 1).Find(&configs).Error; err != nil {
		log.Fatalf("查询价格配置失败: %v", err)
	}

	fmt.Printf("📊 价格配置统计:\n")
	fmt.Printf("  总配置数: %d\n", len(configs))
	
	var defaultCount, userCount int
	for _, config := range configs {
		if config.UserID == 0 {
			defaultCount++
		} else {
			userCount++
		}
	}
	
	fmt.Printf("  默认配置: %d 条\n", defaultCount)
	fmt.Printf("  用户配置: %d 条\n", userCount)

	// 7. 显示配置详情
	fmt.Printf("\n📋 配置详情:\n")
	fmt.Printf("%-6s %-8s %-12s %-10s %-20s %-20s\n", "ID", "UserID", "ServiceType", "Price", "CreatedAt", "UpdatedAt")
	fmt.Println("================================================================================")
	
	for _, config := range configs {
		fmt.Printf("%-6d %-8d %-12d %-10.2f %-20s %-20s\n", 
			config.ID, 
			config.UserID, 
			config.ServiceType, 
			config.Price,
			config.CreatedAt.Format("2006-01-02 15:04:05"),
			config.UpdatedAt.Format("2006-01-02 15:04:05"))
	}

	fmt.Println("\n🎉 价格配置初始化完成！")
	fmt.Println("\n📋 使用说明:")
	fmt.Println("  - UserID = 0: 全局默认价格配置")
	fmt.Println("  - UserID > 0: 特定用户的价格配置")
	fmt.Println("  - ServiceType = 1: 拍照搜题服务")
	fmt.Println("  - Price = 0.01: 每次API调用费用（元）")
}
