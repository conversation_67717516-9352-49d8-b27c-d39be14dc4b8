package main

import (
	"fmt"
	"solve_api/internal/model"
)

// 验证S1实现的测试程序
func main() {
	fmt.Println("=== S1实现验证程序 ===")
	
	// 1. 测试预处理逻辑
	fmt.Println("\n1. 测试预处理逻辑")
	testPreprocessing()
	
	// 2. 测试缓存键生成
	fmt.Println("\n2. 测试缓存键生成")
	testCacheKeyGeneration()
	
	// 3. 测试数据结构
	fmt.Println("\n3. 测试数据结构")
	testDataStructures()
	
	fmt.Println("\n=== 验证完成 ===")
}

// 测试预处理逻辑
func testPreprocessing() {
	// 测试用例1：题目内容为空
	fmt.Println("测试用例1：题目内容为空")
	structure1 := &model.QuestionStructure{
		QuestionType: "单选题",
		QuestionText: "",
		Options: map[string]string{
			"A": "选项A",
			"B": "选项B", 
			"C": "选项C",
			"D": "选项D",
		},
	}
	
	result1, err1 := model.PreprocessQwenResult(structure1)
	if err1 != nil {
		fmt.Printf("✅ 正确：题目为空时返回错误 - %v\n", err1)
	} else {
		fmt.Printf("❌ 错误：题目为空时应该返回错误，但得到了结果 - %+v\n", result1)
	}
	
	// 测试用例2：题目内容包含类型前缀
	fmt.Println("\n测试用例2：题目内容包含类型前缀")
	structure2 := &model.QuestionStructure{
		QuestionType: "单选题",
		QuestionText: "（单选题）以下哪个是正确的？",
		Options: map[string]string{
			"A": "选项A",
			"B": "选项B",
			"C": "选项C", 
			"D": "选项D",
		},
	}
	
	result2, err2 := model.PreprocessQwenResult(structure2)
	if err2 == nil && result2.IsValid {
		if result2.QuestionText == "以下哪个是正确的？" {
			fmt.Printf("✅ 正确：成功移除题目类型前缀\n")
		} else {
			fmt.Printf("❌ 错误：前缀移除失败，结果为：%s\n", result2.QuestionText)
		}
	} else {
		fmt.Printf("❌ 错误：预处理失败 - %v\n", err2)
	}
	
	// 测试用例3：单选题选项不完整
	fmt.Println("\n测试用例3：单选题选项不完整")
	structure3 := &model.QuestionStructure{
		QuestionType: "单选题",
		QuestionText: "测试题目",
		Options: map[string]string{
			"A": "选项A",
			"B": "",  // 空选项
			"C": "选项C",
			"D": "选项D",
		},
	}
	
	result3, err3 := model.PreprocessQwenResult(structure3)
	if err3 != nil || !result3.IsValid {
		fmt.Printf("✅ 正确：选项不完整时返回错误\n")
	} else {
		fmt.Printf("❌ 错误：选项不完整时应该返回错误\n")
	}
	
	// 测试用例4：判断题选项处理
	fmt.Println("\n测试用例4：判断题选项处理")
	structure4 := &model.QuestionStructure{
		QuestionType: "判断题",
		QuestionText: "地球是圆的。",
		Options: map[string]string{
			"A": "选项A",
			"B": "选项B",
		},
	}
	
	result4, err4 := model.PreprocessQwenResult(structure4)
	if err4 == nil && result4.IsValid {
		if result4.OptionsY == "正确" && result4.OptionsN == "错误" &&
		   result4.OptionsA == "" && result4.OptionsB == "" {
			fmt.Printf("✅ 正确：判断题选项处理正确\n")
		} else {
			fmt.Printf("❌ 错误：判断题选项处理失败\n")
			fmt.Printf("   Y: %s, N: %s, A: %s, B: %s\n", 
				result4.OptionsY, result4.OptionsN, result4.OptionsA, result4.OptionsB)
		}
	} else {
		fmt.Printf("❌ 错误：判断题预处理失败 - %v\n", err4)
	}
}

// 测试缓存键生成
func testCacheKeyGeneration() {
	// 创建两个相同内容的预处理结果
	preprocessed1 := &model.PreprocessedQuestion{
		QuestionType: "单选题",
		QuestionText: "测试题目",
		OptionsA:     "选项A",
		OptionsB:     "选项B",
		OptionsC:     "选项C",
		OptionsD:     "选项D",
	}
	
	preprocessed2 := &model.PreprocessedQuestion{
		QuestionType: "单选题", 
		QuestionText: "测试题目",
		OptionsA:     "选项A",
		OptionsB:     "选项B",
		OptionsC:     "选项C",
		OptionsD:     "选项D",
	}
	
	key1 := model.GenerateCacheKeyFromPreprocessed(preprocessed1)
	key2 := model.GenerateCacheKeyFromPreprocessed(preprocessed2)
	
	if key1 == key2 {
		fmt.Printf("✅ 正确：相同内容生成相同缓存键\n")
		fmt.Printf("   缓存键: %s\n", key1)
	} else {
		fmt.Printf("❌ 错误：相同内容应该生成相同缓存键\n")
		fmt.Printf("   键1: %s\n   键2: %s\n", key1, key2)
	}
	
	// 测试不同内容生成不同键
	preprocessed3 := &model.PreprocessedQuestion{
		QuestionType: "单选题",
		QuestionText: "不同的题目",
		OptionsA:     "选项A",
		OptionsB:     "选项B", 
		OptionsC:     "选项C",
		OptionsD:     "选项D",
	}
	
	key3 := model.GenerateCacheKeyFromPreprocessed(preprocessed3)
	
	if key1 != key3 {
		fmt.Printf("✅ 正确：不同内容生成不同缓存键\n")
	} else {
		fmt.Printf("❌ 错误：不同内容应该生成不同缓存键\n")
	}
}

// 测试数据结构
func testDataStructures() {
	// 测试Question结构的新字段
	question := &model.Question{}
	
	// 检查新字段是否存在（通过反射或直接访问）
	fmt.Printf("Question结构包含以下新字段：\n")
	fmt.Printf("- CacheKey: %T\n", question.CacheKey)
	fmt.Printf("- QuestionDoc: %T\n", question.QuestionDoc)
	fmt.Printf("- QuestionImg: %T\n", question.QuestionImg)
	fmt.Printf("- QuestionImgRaw: %T\n", question.QuestionImgRaw)
	fmt.Printf("- OptionsA: %T\n", question.OptionsA)
	fmt.Printf("- OptionsB: %T\n", question.OptionsB)
	fmt.Printf("- OptionsC: %T\n", question.OptionsC)
	fmt.Printf("- OptionsD: %T\n", question.OptionsD)
	fmt.Printf("- OptionsY: %T\n", question.OptionsY)
	fmt.Printf("- OptionsN: %T\n", question.OptionsN)
	fmt.Printf("- Response: %T\n", question.Response)
	fmt.Printf("- RawQwen: %T\n", question.RawQwen)
	fmt.Printf("- RawDeepseek: %T\n", question.RawDeepseek)
	fmt.Printf("- Associates: %T\n", question.Associates)
	
	// 测试FromPreprocessed方法
	preprocessed := &model.PreprocessedQuestion{
		QuestionType: "单选题",
		QuestionText: "测试题目",
		OptionsA:     "选项A",
		OptionsB:     "选项B",
		OptionsC:     "选项C", 
		OptionsD:     "选项D",
	}
	
	question.FromPreprocessed(preprocessed, "http://example.com/image.jpg", 
		"qwen原始数据", "deepseek原始数据", "解析内容", "A")
	
	if question.QuestionDoc == "测试题目" && 
	   question.OptionsA == "选项A" &&
	   question.RawQwen == "qwen原始数据" {
		fmt.Printf("✅ 正确：FromPreprocessed方法工作正常\n")
	} else {
		fmt.Printf("❌ 错误：FromPreprocessed方法有问题\n")
	}
	
	// 测试ToManagementResponse方法
	response := question.ToManagementResponse()
	if response != nil {
		fmt.Printf("✅ 正确：ToManagementResponse方法工作正常\n")
	} else {
		fmt.Printf("❌ 错误：ToManagementResponse方法返回nil\n")
	}
}
