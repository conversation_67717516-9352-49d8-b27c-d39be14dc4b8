QWEN返回数据后，先对其进行预处理，
1.当题目的值为空时，则终止进程并返回图片不标准，请重新拍摄。
2.当题目的值，为（判断题）（多选题）（单选题）开头时移除这些题目类型不属于题目的文字。（部分题目会存在）
3.类型为单选题与多选题的检测；检测abcd四个选项都不为空，则进行下一步，若存在空值，则终止进程返回图片不标准，请重新拍摄。
4.类型判断题强制丢弃选项，将选项渲染为Y:正确。N:错误

5.完成处理后，将所有内容生成缓存键。查找redis，若redis存在则判断val是否存在关联键值，存在则查询mysql将关联键对应的题，一起返回，若不存在关联键则直接返回value，若不存在则降级查找mysql，
若mysql存在，则回传redis，然后在返回对应的value，若mysql不存在则降级提交deepseek。

6.deepseek返回数据后，将json内容解析后。取前面生成的缓存键、用户上传的图片url、Qwen返回的原始数据、deepseek返回的原始数据与解析内容一起存入题库表。
解析规则
题目类型、题目内容、选项a、选项b、选项c、选项d、选项y、选项n、正确答案、答案解析、其中选项的值判断题允许abcd为空，单选与多选允许YN为空。

当redis不存在但mysql存在时，先判断mysql对应的题目是否存在关联键的值，如果有关联键，回传的值需要拼装上面内容以及关联键存入redis的value。如果没有关联键则不需要拼接关联键。

写一个题库管理控制器，用于题库表内容的修改；如下

允许新增问题



重新架构题库表
question_id = 问题ID。自增
cache_key = 缓存键   禁止修改允许为空

question_type = 题目类型。 允许修改
question_doc = 题目内容。允许修改
question_img = 题目图片   允许修改
question_img_raw = 用户图片    允许修改
options_a = 选项A   允许修改
options_b = 选项B    允许修改
options_c = 选项C    允许修改
options_d = 选项D    允许修改
options_y = 选项Y     允许修改
options_n = 选项N    允许修改
answer = A，B/Y/N      允许修改
analysis = 答案解析     允许修改
response = 响应次数   不允许修改
raw_qwen = qwen原始返回的数据。  不允许修改
raw_deepseek = deepseek原始返回的数据。 不允许修改

associates = 关联键。允许修改



关联建的作用时为了以后当出现问题一样的的题目时，管理员手动增加问题，会关联到已经存在的题中，这样为了避免出现非预期的结果，系统会将缓存键对应的题目与关联的题目一起返回。